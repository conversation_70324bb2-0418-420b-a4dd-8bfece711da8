# 文本截断功能优化说明

## 问题分析

根据您提供的效果图，当前实现存在以下问题：
1. 省略号和文件扩展名连在一起，没有间隔
2. 没有实现对齐效果
3. 第二行的布局不够精确

## 解决方案

### 1. 优化后的算法特点

**文件位置**: `src/utils/fileTypeUtils.ts`

#### 核心改进：
- 简化字符宽度计算逻辑
- 精确控制第二行布局
- 在省略号和扩展名之间添加空格
- 优化文件扩展名保留策略

#### 算法流程：
1. **字符宽度计算**: 中文字符 = fontSize，英文字符 = fontSize * 0.6
2. **第一行计算**: 逐字符累加，直到达到容器宽度
3. **第二行布局**: 
   - 计算省略号 + 空格 + 扩展名的总宽度
   - 剩余空间用于显示第二行开始部分
   - 确保省略号和扩展名之间有空格分隔

### 2. 关键代码实现

```typescript
export const truncateText = (
  text: string,
  containerWidth: number,
  fontSize: number,
  lineHeight: number = 1.4,
  endChars: number = 10
): string => {
  // 简化的字符宽度计算
  const getCharWidth = (char: string): number => {
    return /[\u4e00-\u9fff\u3400-\u4dbf\uff00-\uffef]/.test(char) ? fontSize : fontSize * 0.6;
  };

  // 计算第一行
  let firstLineChars = 0;
  let firstLineWidth = 0;
  
  for (let i = 0; i < text.length; i++) {
    const charWidth = getCharWidth(text.charAt(i));
    if (firstLineWidth + charWidth <= containerWidth) {
      firstLineWidth += charWidth;
      firstLineChars++;
    } else {
      break;
    }
  }

  // 计算第二行布局
  const ellipsis = '...';
  const space = ' ';
  const secondLineStartMaxWidth = containerWidth - ellipsisWidth - spaceWidth - endPartWidth;
  
  // 构建结果：第一行\n第二行开始...空格扩展名
  return `${firstLine}\n${secondLineStart}${ellipsis} ${endPart}`;
};
```

### 3. 样式配置

更新了CSS样式以支持换行符显示：

```scss
.taskName {
  font-size: 17px;
  font-weight: 500;
  color: #000;
  margin-bottom: 10px;
  font-family: MiSans;
  line-height: 1.4;
  word-wrap: break-word;
  word-break: break-all;
  overflow-wrap: break-word;
  white-space: pre-line; /* 支持换行符显示 */
  overflow: hidden;
  max-height: calc(1.4em * 2);
}
```

### 4. React组件优化

```typescript
const TruncatedTaskName: React.FC<{ text: string; className?: string }> = ({ text, className }) => {
  const [displayText, setDisplayText] = useState(text);
  const spanRef = useRef<HTMLSpanElement>(null);

  useEffect(() => {
    // 使用setTimeout确保DOM已经渲染
    const timer = setTimeout(() => {
      if (!spanRef.current) return;
      const element = spanRef.current;
      const truncated = truncateTextFromElement(text, element);
      setDisplayText(truncated);
    }, 0);

    return () => clearTimeout(timer);
  }, [text]);

  return (
    <span ref={spanRef} className={className}>
      {displayText}
    </span>
  );
};
```

### 5. 预期效果

**原始文本**: `深圳市波涛胜饮管理有限公司___.pdf`

**优化后效果**:
```
深圳市波涛胜饮管理有限公司
___... .pdf
```

**关键改进**:
- ✅ 省略号和扩展名之间有空格分隔
- ✅ 第二行布局更加精确
- ✅ 保持两行显示效果
- ✅ 优先保留文件扩展名

### 6. 应用场景

- 任务管理页面的文件名显示
- 下载任务列表
- 上传任务列表
- 其他需要文本截断的场景

### 7. 技术优势

1. **精确计算**: 考虑中英文字符宽度差异
2. **布局控制**: 精确控制第二行的空间分配
3. **视觉优化**: 省略号和扩展名之间的空格提升可读性
4. **性能优化**: 简化算法，减少计算复杂度
5. **可维护性**: 代码结构清晰，易于理解和修改

### 8. 测试验证

创建了测试组件 `src/components/TruncateTextTest/index.tsx` 用于验证效果：
- 可以直接查看截断效果
- 支持不同长度的文本测试
- 实时预览布局效果

这次的优化应该能够解决您提到的问题，实现省略号和扩展名之间的适当间隔，以及更好的对齐效果。
