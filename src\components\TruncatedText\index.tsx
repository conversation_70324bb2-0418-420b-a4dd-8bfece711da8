import React, { useEffect, useRef, useState } from 'react';
import styles from './index.module.scss';

interface TruncatedTextProps {
  text: string;
  className?: string;
  maxLines?: number;
  startChars?: number;
  endChars?: number;
}

const TruncatedText: React.FC<TruncatedTextProps> = ({
  text,
  className = '',
  maxLines = 2,
  startChars = 10,
  endChars = 10
}) => {
  const [displayText, setDisplayText] = useState(text);
  const [needsTruncation, setNeedsTruncation] = useState(false);
  const textRef = useRef<HTMLDivElement>(null);
  const hiddenRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const checkTruncation = () => {
      if (!textRef.current || !hiddenRef.current) return;

      const container = textRef.current;
      const hiddenDiv = hiddenRef.current;
      
      // 设置隐藏div的样式与容器相同
      const containerStyles = window.getComputedStyle(container);
      hiddenDiv.style.width = containerStyles.width;
      hiddenDiv.style.fontSize = containerStyles.fontSize;
      hiddenDiv.style.fontFamily = containerStyles.fontFamily;
      hiddenDiv.style.fontWeight = containerStyles.fontWeight;
      hiddenDiv.style.lineHeight = containerStyles.lineHeight;
      hiddenDiv.style.letterSpacing = containerStyles.letterSpacing;
      
      // 设置原始文本
      hiddenDiv.textContent = text;
      
      // 计算行高
      const lineHeight = parseFloat(containerStyles.lineHeight) || parseFloat(containerStyles.fontSize) * 1.2;
      const maxHeight = lineHeight * maxLines;
      
      // 检查是否需要截断
      if (hiddenDiv.scrollHeight > maxHeight) {
        setNeedsTruncation(true);
        
        // 如果文本长度小于等于开头+结尾字符数，直接显示
        if (text.length <= startChars + endChars + 3) { // +3 for "..."
          setDisplayText(text);
          return;
        }

        // 对于文件名，尝试保留文件扩展名
        const lastDotIndex = text.lastIndexOf('.');
        let startText = text.substring(0, startChars);
        let endText = text.substring(text.length - endChars);

        // 如果有文件扩展名且扩展名不太长，优先保留完整扩展名
        if (lastDotIndex > 0 && text.length - lastDotIndex <= 8) {
          const extension = text.substring(lastDotIndex);
          const nameWithoutExt = text.substring(0, lastDotIndex);

          if (nameWithoutExt.length > startChars) {
            startText = nameWithoutExt.substring(0, startChars);
            endText = extension;
          }
        }

        const truncatedText = `${startText}...${endText}`;
        setDisplayText(truncatedText);
      } else {
        setNeedsTruncation(false);
        setDisplayText(text);
      }
    };

    // 初始检查
    checkTruncation();

    // 监听窗口大小变化
    const resizeObserver = new ResizeObserver(checkTruncation);
    if (textRef.current) {
      resizeObserver.observe(textRef.current);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, [text, maxLines, startChars, endChars]);

  return (
    <>
      <div
        ref={textRef}
        className={`${styles.truncatedText} ${className}`}
        title={needsTruncation ? text : undefined}
      >
        {displayText}
      </div>
      {/* 隐藏的测量div */}
      <div
        ref={hiddenRef}
        className={styles.hiddenMeasure}
        aria-hidden="true"
      />
    </>
  );
};

export default TruncatedText;
