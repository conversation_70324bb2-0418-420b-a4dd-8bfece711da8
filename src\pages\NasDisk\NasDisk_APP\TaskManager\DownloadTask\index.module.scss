.unifiedContainer {
  height: 100%;
  overflow-y: auto;
  padding: 0 16px;
}

.sectionContainer {
  margin-bottom: 24px;

  .header {
    display: flex;
    justify-content: space-between;
    font-family: MiSans;
      color: #767676;
    align-items: center;
    margin-bottom: 16px;

    .headerArrow {
      margin-left: 12px;
    }

    .clearText {
      line-height: 100%;
      background-color: #F4F5F8;
      font-size: 11px;
      border-radius: 20px;
      padding: 6px 10px;
      width: 65px;
      text-align: center;
      color: #000;
      font-family: MiSans;
    }

    // 重试按钮样式
    .retryButton {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px; // 图标和文字之间的间距

      span {
        display: flex;
        align-items: center;
      }
    }

    // 全选文本样式
    .selectAllText {
      color: #2F2F2F;
      font-size: 14px;
      cursor: pointer;
      padding: 4px 8px;

      &:active {
        opacity: 0.6;
      }
    }

    .selectAllCheckbox {
      :global(.adm-checkbox-content) {
        font-size: 14px;
        // color: #007aff;
      }

      :global(.adm-checkbox-checked) {
        background-color: #007aff;
      }

      :global(.adm-checkbox-icon-checked) {
        background-color: #007aff;
        border-color: #007aff;
      }
    }
  }

  .taskItem {
    margin-bottom: 12px;
  }

  .taskAll {
    display: flex;
    align-items: center;
  }

  .folderIcon {
    width: 40px;
    height: 40px;
    margin-right: 12px;
  }

  .taskMiddle {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .taskName {
    font-size: 17px;
    font-weight: 500;
    color: #000;
    margin-bottom: 10px;
    font-family: MiSans;
    line-height: 1.4;
    word-wrap: break-word;
    word-break: break-all;
    overflow-wrap: break-word;
    white-space: pre; /* 保留空格和换行符 */
    overflow: hidden;
    max-height: calc(1.4em * 2);
  }

  .progressBar {
  height: 4px;
  margin-bottom: 4px;
  }

  .bottomRow {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .fileSize {
    font-size: 14px;
    color: #666;
  }

  .statusText {
    font-size: 14px;
    color: #666;
  }

  .taskRight {
    margin-left: 16px;
    color:#767676;
    font-family: MiSans;
    font-size: 12px;
    :global(.adm-checkbox) {
      :global(.adm-checkbox-icon) {
        // border-color: #FFFFFF;
        width: 20px;
        height: 20px;
      }

      :global(.adm-checkbox-icon-checked) {
        background-color: #007aff;
        border-color: #007aff;
      }
    }
  }

  .pauseButton {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #666;
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .playButton {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #32BAC0;
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .retryButton {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #ff4d4f;
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .completedIndicator {
    color: #007aff;
    font-weight: bold;
  }

  .completedInfo {
    margin-top: 4px;
  }

  .completedText {
    font-size: 12px;
    color: #999;
  }

  // 加载状态样式
  .loadingContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;

    p {
      margin-top: 16px;
      color: #666;
      font-size: 14px;
    }
  }

  // 错误状态样式
  .errorContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
  }
}


.completedContainer {
  margin-top: 16px;
  background-color: #fff;
  border-radius: 12px;
  margin-bottom: 70px;
  /* 为底部删除栏留出空间 */
}

.deleteBar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 64px;
  background-color: #ffffff;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;

  .deleteContent {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px;

    .deleteIcon {
      font-size: 24px;
    }

    .deleteText {
      font-size: 14px;
      color: #333;
      margin-top: 4px;
    }
  }
}

// 空状态样式
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
  padding: 40px 20px;

  .emptyIcon {
    width: 120px;
    height: 120px;
    background-color: #f0f0f0;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;

    img {
      width: 60px;
      height: 60px;
    }
  }

  .emptyText {
    color: #999;
    font-size: 16px;
    text-align: center;
  }
}