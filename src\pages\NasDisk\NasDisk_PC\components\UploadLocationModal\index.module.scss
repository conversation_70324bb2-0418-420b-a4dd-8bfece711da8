.uploadLocationModal {
  :global {
    .ant-modal-content {
      border-radius: 32px;
      overflow: hidden;
    }
    
    .ant-modal-header {
      padding: 0;
      margin-bottom: 0;
      border-bottom: none;
    }
    
    .ant-modal-title {
      padding: 0;
      margin: 0;
      line-height: 1;
    }
    
    .ant-modal-body {
      padding: 0;
    }
  }
}

.modalHeader {
  display: flex;
  align-items: center;
  padding: 16px 10px;
  border-bottom: 1px solid var(--border-color);
  
  .backIcon {
    font-size: 18px;
    color: var(--text-color);
    cursor: pointer;
    margin-right: 16px;
  }
  
  .modalTitle {
    flex: 1;
    text-align: center;
    font-size: 18px;
    font-weight: 500;
    color: var(--text-color);
    margin-right: 34px; /* 平衡左侧返回按钮的宽度，使标题居中 */
  }
}

.breadcrumbHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 10px;
  border-bottom: 1px solid var(--border-color);
}

.breadcrumbContainer {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  
  .breadcrumbItem {
    padding: 4px 12px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    
    &.breadcrumbLink {
      color: rgba(140, 147, 176, 1);
      background-color: rgba(140, 147, 176, 0.1);
    }
    
    &.breadcrumbCurrent {
      color: rgba(255, 178, 29, 1);
      background-color: rgba(255, 178, 29, 0.15);
    }
  }
  
  .breadcrumbSeparator {
    margin: 0 8px;
    color: var(--text-secondary-color);
  }
}

.selectAllContainer {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 4px 10px;
  border-radius: 16px;
  transition: all 0.2s;
  
  .selectAllText {
    font-size: 14px;
    color: var(--text-color);
  }
}

.modalContent {
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .fileListContainer {
    flex: 1;
    overflow-y: auto;
    padding: 0;
    
    .fileList {
      width: 100%;
      
      :global {
        .ant-list-item {
          padding: 12px 24px;
          border-bottom: 1px solid var(--border-color);
          
          &:hover {
            background-color: var(--hover-background-color);
          }
        }
      }
      
      .fileItem {
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        
        &.selectedItem {
          background-color: var(
            --item-selected-background-color,
            rgba(0, 0, 0, 0.05)
          );
        }
        
        .fileContent {
          display: flex;
          align-items: center;
          flex: 1;
          cursor: pointer;
          transition: all 0.2s;
          
          &.selectedContent {
            .fileName {
              color: var(--primary-color);
              font-weight: 600;
            }
          }
          
          &:hover {
            .fileName {
              color: var(--primary-color);
            }
          }
          
          .fileIcon {
            width: 36px;
            height: 36px;
            margin-right: 12px;
          }
          
          .fileInfo {
            flex: 1;
            
            .fileName {
              font-family: MiSans W;
              font-weight: 500;
              font-size: 16px;
              line-height: 22px;
              color: var(--text-color);
              margin-bottom: 4px;
            }
            
            .fileDetails {
              font-size: 12px;
              color: var(--text-secondary-color);
            }
          }
        }
        
        .arrowIcon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 32px;
          height: 32px;
          border-radius: 50%;
          cursor: pointer;
          transition: all 0.2s;
          color: var(--text-secondary-color);
          
          &:hover {
            background-color: var(--hover-background-color);
            color: var(--primary-color) !important;
          }
        }
      }
    }
    
    .loadingContainer {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: var(--text-secondary-color);
    }
    
    .emptyContainer {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      
      .emptyText {
        color: var(--text-secondary-color);
        font-size: 14px;
        margin-bottom: 16px;
      }
    }
  }
  
  .footerContainer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 0;
    border-top: 1px solid var(--border-color);

    .footerLeft {
      flex: 0 0 36%;
      display: flex;
      justify-content: center;
    }
    
    .footerRight {
      flex: 1;
      display: flex;
      justify-content: flex-end;
    }

    .confirmButton {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
      height: 50px;
      min-width: 300px;
      border-radius: 20px;
      
      &:disabled {
        background-color: var(--primary-color);
        color: rgba(255, 255, 255, 1);
        border: none;
        opacity: 0.3;
      }
    }
  }
}

.newFolderButton {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 7px 0;
  border-radius: 4px;
  width: 100%;
  min-width: 120px;
  max-width: 220px;
  background: rgba(245, 245, 245, 1);
  border-radius: 20px;
  color: rgba(112, 112, 112, 1);
  
  &:hover {
    background: rgba(245, 245, 245, 1);
  }
  
  .folderIconContainer {
    width: 36px;
    height: 36px;
    margin-right: 12px;
    
    .folderIcon {
      width: 100%;
      height: 100%;
    }
  }
  
  .newFolderText {
    font-size: 14px;
    color: var(--text-color);
  }
} 