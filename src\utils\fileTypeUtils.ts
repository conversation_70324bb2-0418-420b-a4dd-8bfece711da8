import folderIcon from "@/Resources/nasDiskImg/file-icon.png";
import pdfIcon from "@/Resources/nasDiskImg/pdfIcon.png";
import zipIcon from "@/Resources/nasDiskImg/zipIcon.png";
import pptIcon from "@/Resources/nasDiskImg/pptIcon.png";
import wordIcon from "@/Resources/nasDiskImg/wordIcon.png";
import xlsIcon from "@/Resources/nasDiskImg/xlsIcon.png";
import textIcon from "@/Resources/nasDiskImg/textIcon.png";
import imageIcon from "@/Resources/nasDiskImg/imgIcon.png";
import videoIcon from "@/Resources/nasDiskImg/videoIcon.png";
import audioIcon from "@/Resources/nasDiskImg/musicIcon.png";
// TaskManager专用图标
import btIcon from "@/Resources/nasDiskImg/btIcon.png";
import otherIcon from "@/Resources/nasDiskImg/otherIcon.png";

// 文件类型定义
export const FileTypes = {
  IMAGE: 'image',
  VIDEO: 'video',
  AUDIO: 'audio',
  WORD: 'word',
  EXCEL: 'excel',
  PPT: 'ppt',
  TEXT: 'text',
  ZIP: 'zip',
  PDF: 'pdf',
  FOLDER: 'folder',
  BT: 'bt', // BT种子文件
  UNKNOWN: 'unknown',
} as const;

export type FileType = typeof FileTypes[keyof typeof FileTypes];

// 文件类型到对应扩展名的映射
export const fileTypeExtensions: Record<FileType, string[]> = {
  [FileTypes.IMAGE]: ["jpg", "jpeg", "png", "gif", "bmp", "webp", "svg", "ico"],
  [FileTypes.VIDEO]: ["mp4", "avi", "mov", "wmv", "flv", "mkv", "webm", "m4v", "3gp"],
  [FileTypes.AUDIO]: ["mp3", "wav", "ogg", "flac", "aac", "wma", "m4a", "ape", "ac3"],
  [FileTypes.WORD]: ["doc", "docx", "rtf"],
  [FileTypes.EXCEL]: ["xls", "xlsx", "csv"],
  [FileTypes.PPT]: ["ppt", "pptx"],
  [FileTypes.TEXT]: ["txt", "log", "md", "json", "xml", "html", "css", "js", "ts", "py", "java", "cpp", "c", "h"],
  [FileTypes.ZIP]: ["zip", "rar", "7z", "tar", "gz", "bz2", "xz"],
  [FileTypes.PDF]: ["pdf"],
  [FileTypes.BT]: ["torrent"],
  [FileTypes.FOLDER]: [],
  [FileTypes.UNKNOWN]: [],
};

// 文件类型到图标的映射
export const fileTypeIcons: Record<FileType, string> = {
  [FileTypes.IMAGE]: imageIcon,
  [FileTypes.VIDEO]: videoIcon,
  [FileTypes.AUDIO]: audioIcon,
  [FileTypes.WORD]: wordIcon,
  [FileTypes.EXCEL]: xlsIcon,
  [FileTypes.PPT]: pptIcon,
  [FileTypes.TEXT]: textIcon,
  [FileTypes.ZIP]: zipIcon,
  [FileTypes.PDF]: pdfIcon,
  [FileTypes.BT]: btIcon,
  [FileTypes.FOLDER]: folderIcon,
  [FileTypes.UNKNOWN]: folderIcon,
};

// 构建扩展名到图标的映射表
export const getFileExtensionIconMap = () => {
  const map: Record<string, string> = {};
  
  Object.entries(fileTypeExtensions).forEach(([type, extensions]) => {
    const iconPath = fileTypeIcons[type as FileType];
    extensions.forEach(ext => {
      map[ext] = iconPath;
    });
  });
  
  return map;
};

// 根据文件名和是否为目录获取图标
export interface FileInfo {
  name: string;
  isDirectory?: boolean;
}

export const getFileIcon = (file: FileInfo): string => {
  // 如果是文件夹，直接返回文件夹图标
  if (file.isDirectory) {
    return fileTypeIcons[FileTypes.FOLDER];
  }

  // 获取文件扩展名
  const extension = file.name.split(".").pop()?.toLowerCase() || "";
  
  // 获取映射表
  const extensionMap = getFileExtensionIconMap();
  
  // 根据扩展名返回对应图标，如果没有匹配则返回默认图标
  return extensionMap[extension] || fileTypeIcons[FileTypes.UNKNOWN];
};

// 根据文件名判断文件类型
export const getFileType = (fileName: string, isDirectory?: boolean): FileType => {
  if (isDirectory) {
    return FileTypes.FOLDER;
  }

  const extension = fileName.split(".").pop()?.toLowerCase() || "";

  for (const [type, extensions] of Object.entries(fileTypeExtensions)) {
    if (extensions.includes(extension)) {
      return type as FileType;
    }
  }

  return FileTypes.UNKNOWN;
};

// ==================== TaskManager专用函数 ====================

/**
 * TaskManager专用：根据文件路径和源类型获取对应的图标
 * @param filePath 文件路径
 * @param srcType 源类型：'dir' 表示文件夹，其他值或undefined表示文件
 * @returns 图标路径
 */
export const getTaskManagerFileIcon = (filePath: string, srcType?: string): string => {
  if (!filePath) return folderIcon;

  // 如果是文件夹类型，返回文件夹图标
  if (srcType === 'dir') {
    return folderIcon;
  }

  // 获取文件扩展名
  const lastDotIndex = filePath.lastIndexOf('.');
  if (lastDotIndex === -1) return folderIcon; // 没有扩展名，使用默认图标

  const extension = filePath.substring(lastDotIndex + 1).toLowerCase();

  // 获取映射表
  const extensionMap = getFileExtensionIconMap();

  // 根据扩展名返回对应图标，如果没有匹配则返回其他文件图标
  return extensionMap[extension] || otherIcon;
};

/**
 * TaskManager专用：根据文件路径获取对应的图标（APP端使用）
 * @param filePath 文件路径
 * @returns 图标路径
 */
export const getTaskManagerFileIconForApp = (filePath: string): string => {
  if (!filePath) return folderIcon;

  // 获取文件扩展名
  const lastDotIndex = filePath.lastIndexOf('.');
  if (lastDotIndex === -1) return folderIcon; // 没有扩展名，使用默认图标

  const extension = filePath.substring(lastDotIndex + 1).toLowerCase();

  // 获取映射表
  const extensionMap = getFileExtensionIconMap();

  // 根据扩展名返回对应图标，如果没有匹配则返回其他文件图标
  return extensionMap[extension] || otherIcon;
};

/**
 * 文本截断函数，实现两行显示效果
 * 如果文本超过两行，在第二行中间添加省略号，省略号后拼接原文最后的内容
 * @param text 原始文本
 * @param containerWidth 容器宽度（像素）
 * @param fontSize 字体大小（像素）
 * @param lineHeight 行高倍数，默认1.4
 * @param endChars 末尾保留字符数，默认10
 * @returns 截断后的文本
 */
export const truncateText = (
  text: string,
  containerWidth: number,
  fontSize: number,
  lineHeight: number = 1.4,
  endChars: number = 10
): string => {
  if (!text || !containerWidth || !fontSize) {
    return text;
  }

  // 更精确地估算字符宽度（考虑中英文混合）
  const getTextWidth = (str: string): number => {
    let width = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charAt(i);
      // 中文字符、全角字符占用更多空间
      if (/[\u4e00-\u9fff\u3400-\u4dbf\uff00-\uffef]/.test(char)) {
        width += fontSize; // 中文字符约等于fontSize宽度
      } else {
        width += fontSize * 0.6; // 英文字符约为fontSize的0.6倍
      }
    }
    return width;
  };

  // 计算能容纳的最大字符数
  const maxWidthForTwoLines = containerWidth * 2;

  // 如果文本宽度不超过两行，直接返回
  if (getTextWidth(text) <= maxWidthForTwoLines) {
    return text;
  }

  // 二分查找第一行能容纳的最大字符数
  const findMaxCharsForLine = (maxWidth: number, startIndex: number = 0): number => {
    let low = 1;
    let high = text.length - startIndex;
    let maxChars = 1;

    while (low <= high) {
      const mid = Math.floor((low + high) / 2);
      const testText = text.substring(startIndex, startIndex + mid);

      if (getTextWidth(testText) <= maxWidth) {
        maxChars = mid;
        low = mid + 1;
      } else {
        high = mid - 1;
      }
    }

    return maxChars;
  };

  // 计算第一行字符数
  const firstLineChars = findMaxCharsForLine(containerWidth);
  const firstLinePart = text.substring(0, firstLineChars);

  // 计算第二行可用空间（需要为省略号和末尾文本留空间）
  const ellipsis = '...';
  const ellipsisWidth = getTextWidth(ellipsis);

  // 对于文件名，优先保留扩展名
  const lastDotIndex = text.lastIndexOf('.');
  let endPart = text.substring(text.length - endChars);

  if (lastDotIndex > 0 && text.length - lastDotIndex <= 8) {
    const extension = text.substring(lastDotIndex);
    endPart = extension;
  }

  const endPartWidth = getTextWidth(endPart);
  const secondLineAvailableWidth = containerWidth - ellipsisWidth - endPartWidth;

  // 如果第二行空间不够，简化处理
  if (secondLineAvailableWidth <= 0) {
    const simpleMaxChars = findMaxCharsForLine(maxWidthForTwoLines - getTextWidth('...'));
    return text.substring(0, simpleMaxChars) + '...';
  }

  // 计算第二行开始部分的字符数
  const secondLineStartChars = findMaxCharsForLine(secondLineAvailableWidth, firstLineChars);
  const secondLineStart = text.substring(firstLineChars, firstLineChars + secondLineStartChars);

  return `${firstLinePart}${secondLineStart}${ellipsis}${endPart}`;
};

/**
 * 从DOM元素获取样式信息并截断文本
 * @param text 原始文本
 * @param element DOM元素
 * @param endChars 末尾保留字符数，默认10
 * @returns 截断后的文本
 */
export const truncateTextFromElement = (
  text: string,
  element: HTMLElement,
  endChars: number = 10
): string => {
  if (!text || !element) {
    return text;
  }

  const styles = window.getComputedStyle(element);
  const containerWidth = element.offsetWidth;
  const fontSize = parseFloat(styles.fontSize);
  const lineHeight = parseFloat(styles.lineHeight) / fontSize || 1.4;

  return truncateText(text, containerWidth, fontSize, lineHeight, endChars);
};