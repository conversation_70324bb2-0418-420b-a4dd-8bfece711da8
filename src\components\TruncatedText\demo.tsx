import React from 'react';
import TruncatedText from './index';

const TruncatedTextDemo: React.FC = () => {
  const testTexts = [
    "个人所得税APP端填报指南-填报指南-小米人力资源部.ppt",
    "这是一个非常长的文件名用来测试截断效果.docx",
    "短文件名.txt",
    "非常非常非常非常非常非常非常非常非常非常长的文件名没有扩展名",
    "测试中文字符的截断效果这里有很多中文字符.pdf",
    "English_file_name_with_very_long_content_for_testing.xlsx"
  ];

  return (
    <div style={{ padding: '20px', maxWidth: '300px' }}>
      <h3>TruncatedText 组件测试</h3>
      {testTexts.map((text, index) => (
        <div key={index} style={{ marginBottom: '20px', border: '1px solid #ccc', padding: '10px' }}>
          <div style={{ marginBottom: '5px', fontSize: '12px', color: '#666' }}>
            原文本: {text}
          </div>
          <TruncatedText
            text={text}
            maxLines={2}
            startChars={10}
            endChars={10}
            className=""
          />
        </div>
      ))}
    </div>
  );
};

export default TruncatedTextDemo;