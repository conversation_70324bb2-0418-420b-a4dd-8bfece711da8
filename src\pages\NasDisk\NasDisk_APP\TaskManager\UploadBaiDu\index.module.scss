// 从 DownloadTask 复制样式，用于上传百度网盘组件
.unifiedContainer {
  width: 100%;
  overflow-y: auto;
  padding: 0 16px;
}

.sectionContainer {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.header {
  display: flex;
  justify-content: space-between;
  font-family: MiSans;
  color: #767676;
  align-items: center;
  margin-bottom: 16px;

  .selectAllCheckbox {
    :global {
      .adm-checkbox {
        --icon-size: 18px;
      }
    }
  }

  .clearText {
    line-height: 100%;
    background-color: #F4F5F8;
    font-size: 11px;
    width: 65px;
    padding: 6px 10px;
    border-radius: 20px;
    text-align: center;
    color: #000;
    font-family: MiSans;
  }


  // 全选文本样式
  .selectAllText {
    color: #2F2F2F;
    font-size: 14px;
    cursor: pointer;
    padding: 4px 8px;

    &:active {
      opacity: 0.6;
    }
  }

  // 重试按钮样式
  .retryButton {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px; // 图标和文字之间的间距

    span {
      display: flex;
      align-items: center;
    }
  }
}

.taskItem {
  margin: 0;
  padding: 0;

  &:last-child {
    border-bottom: none;
  }

  :global {
    .adm-list-item-content {
      padding: 0;
    }
  }
}

.taskAll {
  display: flex;
  align-items: center;
  // padding: 12px 0px;
  width: 100%;
  gap: 12px;
}

.folderIcon {
  width: 40px;
  height: 40px;
  flex-shrink: 0;
}

.taskMiddle {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.taskName {
  font-size: 17px;
  font-weight: 500;
  color: #000;
  margin-bottom: 10px;
  font-family: MiSans;
  line-height: 1.4;
  word-wrap: break-word;
  word-break: break-all;
  overflow-wrap: break-word;
  white-space: pre; /* 保留空格和换行符 */
  overflow: hidden;
  max-height: calc(1.4em * 2);
}


.progressBar {
  height: 4px;
  margin-bottom: 4px;
}

.bottomRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #666;
}

.fileSize {
  font-size: 14px;
  color: #666;
}

.statusText {
  font-size: 14px;
  color: #666;
}

.taskRight {
  margin-left: 16px;
  color: #767676;
  font-family: MiSans;
  font-size: 12px;
}

.pauseButton,
.playButton,
.retryButton {
  background: none;
  border: none;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: #f5f5f5;
  }

  &:active {
    background-color: #e6f7ff;
  }
}

.storageLocation {
  font-size: 12px;
  color: #999;
  text-align: right;
}