import React, { useRef, useEffect, useState } from 'react';
import { truncateTextFromElement } from '@/utils/fileTypeUtils';

const TruncateTextTest: React.FC = () => {
  const [displayText, setDisplayText] = useState('');
  const containerRef = useRef<HTMLDivElement>(null);
  
  const testText = "深圳市波涛胜饮管理有限公司___.pdf";

  useEffect(() => {
    if (containerRef.current) {
      const truncated = truncateTextFromElement(testText, containerRef.current);
      setDisplayText(truncated);
    }
  }, [testText]);

  return (
    <div style={{ padding: '20px', maxWidth: '300px' }}>
      <h3>文本截断测试</h3>
      <div style={{ marginBottom: '20px' }}>
        <strong>原始文本:</strong> {testText}
      </div>
      <div 
        ref={containerRef}
        style={{
          fontSize: '17px',
          fontWeight: 500,
          fontFamily: 'MiSans',
          lineHeight: 1.4,
          wordWrap: 'break-word',
          wordBreak: 'break-all',
          overflowWrap: 'break-word',
          whiteSpace: 'pre-line',
          overflow: 'hidden',
          maxHeight: 'calc(1.4em * 2)',
          border: '1px solid #ccc',
          padding: '10px',
          width: '200px'
        }}
      >
        {displayText}
      </div>
    </div>
  );
};

export default TruncateTextTest;
