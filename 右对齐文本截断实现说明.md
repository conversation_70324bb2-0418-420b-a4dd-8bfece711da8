# 右对齐文本截断实现说明

## 需求理解

根据您的要求，需要实现以下效果：
```
深圳市菠涛胜餐饮管理有限公司_-------
-----------------发票...金额230.79元.pdf
```

**关键要求**:
1. 省略号和扩展名连在一起，不要空格分隔
2. 第二行的末尾内容（省略号+扩展名）与第一行末尾对齐
3. 这样可以更清楚地看到文件的尾部内容

## 实现方案

### 1. 算法优化

**核心思路**: 使用空格填充来实现右对齐效果

```typescript
// 计算需要填充的空格数量来实现右对齐
const actualSecondLineStartWidth = getTextWidth(secondLineStart);
const actualEllipsisAndEndWidth = getTextWidth(ellipsis + endPart);
const usedWidth = actualSecondLineStartWidth + actualEllipsisAndEndWidth;
const remainingWidth = containerWidth - usedWidth;

// 计算需要多少个空格来填充剩余空间
const spaceWidth = getTextWidth(' ');
const spacesNeeded = Math.floor(remainingWidth / spaceWidth);
const paddingSpaces = ' '.repeat(Math.max(0, spacesNeeded));

// 构建最终结果：第二行开始部分 + 填充空格 + 省略号+扩展名
return `${firstLine}\n${secondLineStart}${paddingSpaces}${ellipsis}${endPart}`;
```

### 2. 样式配置

使用 `white-space: pre` 来保留空格和换行符：

```scss
.taskName {
  font-size: 17px;
  font-weight: 500;
  color: #000;
  margin-bottom: 10px;
  font-family: MiSans;
  line-height: 1.4;
  word-wrap: break-word;
  word-break: break-all;
  overflow-wrap: break-word;
  white-space: pre; /* 保留空格和换行符 */
  overflow: hidden;
  max-height: calc(1.4em * 2);
}
```

### 3. 实现效果

**输入文本**: `深圳市菠涛胜餐饮管理有限公司_发票金额230.79元.pdf`

**预期输出**:
```
深圳市菠涛胜餐饮管理有限公司_
发票                    ...金额230.79元.pdf
```

**关键特点**:
- 省略号和扩展名连在一起：`...金额230.79元.pdf`
- 通过空格填充实现右对齐
- 第二行末尾与第一行末尾对齐
- 清楚显示文件尾部内容

### 4. 技术实现细节

#### 4.1 字符宽度计算
```typescript
const getCharWidth = (char: string): number => {
  return /[\u4e00-\u9fff\u3400-\u4dbf\uff00-\uffef]/.test(char) ? fontSize : fontSize * 0.6;
};
```

#### 4.2 第一行计算
```typescript
let firstLineChars = 0;
let firstLineWidth = 0;

for (let i = 0; i < text.length; i++) {
  const charWidth = getCharWidth(text.charAt(i));
  if (firstLineWidth + charWidth <= containerWidth) {
    firstLineWidth += charWidth;
    firstLineChars++;
  } else {
    break;
  }
}
```

#### 4.3 第二行布局计算
```typescript
// 计算省略号+扩展名的总宽度
const ellipsisAndEndWidth = ellipsisWidth + endPartWidth;

// 计算第二行开始部分的最大宽度
const secondLineStartMaxWidth = containerWidth - ellipsisAndEndWidth;

// 计算填充空格实现右对齐
const remainingWidth = containerWidth - usedWidth;
const spacesNeeded = Math.floor(remainingWidth / spaceWidth);
const paddingSpaces = ' '.repeat(Math.max(0, spacesNeeded));
```

### 5. 应用场景

- ✅ 任务管理页面文件名显示
- ✅ 下载任务列表
- ✅ 上传任务列表
- ✅ 文件浏览器
- ✅ 其他需要文件名截断的场景

### 6. 优势

1. **视觉对齐**: 第二行末尾与第一行末尾对齐，视觉效果更好
2. **信息完整**: 省略号和扩展名连在一起，更容易识别文件类型
3. **空间利用**: 充分利用容器宽度，显示更多有用信息
4. **用户体验**: 清楚显示文件尾部内容，便于用户识别

### 7. 修改的文件

**核心算法**:
- `src/utils/fileTypeUtils.ts` - 优化截断算法，实现右对齐

**样式文件**:
- `src/pages/NasDisk/NasDisk_APP/TaskManager/DownloadTask/index.module.scss`
- `src/pages/NasDisk/NasDisk_APP/TaskManager/UploadBaiDu/index.module.scss`

**测试组件**:
- `src/components/TruncateTextTest/index.tsx` - 验证效果

### 8. 关键改进

- **省略号连接**: `...金额230.79元.pdf` 而不是 `... 金额230.79元.pdf`
- **右对齐**: 使用空格填充实现精确对齐
- **样式优化**: 使用 `white-space: pre` 保留空格
- **算法简化**: 更直观的空格填充方式

这个实现应该能够完全满足您的需求，实现省略号和扩展名连在一起，并且第二行末尾与第一行末尾对齐的效果。
