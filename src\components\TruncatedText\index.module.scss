.truncatedText {
  word-wrap: break-word;
  word-break: break-all;
  overflow-wrap: break-word;
  line-height: 1.4;
  max-height: calc(1.4em * 2); /* 最多显示2行 */
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  white-space: normal;
  /* 确保文本能够正确换行 */
  hyphens: auto;
  -webkit-hyphens: auto;
  -moz-hyphens: auto;
}

.hiddenMeasure {
  position: absolute;
  top: -9999px;
  left: -9999px;
  visibility: hidden;
  pointer-events: none;
  white-space: normal;
  word-wrap: break-word;
  word-break: break-all;
  overflow-wrap: break-word;
}
