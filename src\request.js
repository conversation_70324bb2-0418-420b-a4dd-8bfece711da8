import axios from 'axios'
import { Toast } from 'antd-mobile'
import { DotLoading } from 'antd-mobile'
import ReactDOM from 'react-dom';
import './assets/index.css';
import { getDeviceInfo, fetchDeviceInfo } from "./utils/DeviceType";

let deviceInfo = null;
// 默认域名
//获取当前域名
const initialize = async () => {
  const hostWithoutPort = window.location.host.replace(/:\d+$/, '');
  try {
    await fetchDeviceInfo(); // 获取设备信息
    deviceInfo = getDeviceInfo();
    const cgiPort = deviceInfo.cgiPort;
    if (!deviceInfo || !deviceInfo.cgiPort) {
      throw new Error('设备信息或 cgiPort 不存在');
    }
    // 配置 axios 的 baseURL
    axios.defaults.baseURL = `https://${hostWithoutPort}:${cgiPort}/cgi-bin/luci/`;
    console.log('Axios baseURL:', axios.defaults.baseURL);
    // alert(`初始化成功!${axios.defaults.baseURL}`);
  } catch (error) {
    axios.defaults.baseURL = `https://${hostWithoutPort}:443/cgi-bin/luci/`; // 设置默认的打包 baseURL
    console.error('Axios初始化失败:', error.message);
    // alert(`初始化失败!${axios.defaults.baseURL}`);
  }
};
await initialize();
axios.defaults.baseURL = `https://*************:443/cgi-bin/luci/`; // 设置默认的打包 baseURL
console.log(axios.defaults.baseURL)
// 配置请求头
axios.defaults.headers["Content-Type"] = "application/json";
// 响应时间
axios.defaults.timeout = 10000;
//请求拦截器
axios.interceptors.request.use(
  config => {
    // 检查是否需要显示loading，默认显示
    if (config.showLoading !== false) {
      showLoading();//显示加载动画
    }
    if (deviceInfo && deviceInfo.cgiToken) {
      // 设置统一的请求header
      config.headers.authorization = deviceInfo.cgiToken //授权(每次请求把token带给后台)
    } else {
      config.headers.authorization = '0vRI6WN0vneArNFwSJBsOFxTf7XPb3og';
    }
    return config;
  },
  error => {
    hideLoading();//关闭加载动画
    return Promise.reject(error);
  }
);

//响应拦截器
axios.interceptors.response.use(
  response => {
    // 检查是否需要隐藏loading，默认隐藏
    if (response.config.showLoading !== false) {
      hideLoading();//关闭加载动画
    }
    return response;
  },
  error => {
    hideLoading();//关闭加载动画
    return Promise.resolve(error.response);
  }
);

// 处理请求返回的数据
function checkStatus(response) {
  return new Promise((resolve, reject) => {
    if (response && (response.status === 200 || response.status === 304 || response.status === 400)) {
      resolve(response.data);
    } else {
      Toast.show({
        content: '网络异常，请检查网络连接是否正常',
        afterClose: () => {
          console.log('after')
        },
      })
      reject(response);
    }
  });
}

const request = {
  post(url, params, config = {}) {
    return axios({
      method: "post",
      url,
      data: params,
      ...config // 传递额外的配置，包括showLoading
    }).then(response => {
      return checkStatus(response);
    });
  },
  get(url, params, config = {}) {
    return axios({
      method: "get",
      url,
      params,
      ...config // 传递额外的配置，包括showLoading
    }).then(response => {
      return checkStatus(response);
    });
  }
};
export default request;

// 显示加载动画
function showLoading() {
  // 检查是否已经存在加载动画，避免重复创建
  if (document.getElementById('loading')) {
    return;
  }

  let dom = document.createElement('div');
  dom.setAttribute('id', 'loading');
  document.body.appendChild(dom);
  ReactDOM.render(<DotLoading color='primary' />, dom);
}
// 隐藏加载动画
function hideLoading() {
  const loadingElement = document.getElementById('loading');
  if (loadingElement) {
    ReactDOM.unmountComponentAtNode(loadingElement); // 卸载 React 组件
    document.body.removeChild(loadingElement); // 移除 DOM 节点
  }
}