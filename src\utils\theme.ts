const themes: Record<string, Record<string, string>> = {
  light: {
    '--background-color': '#ffffff',
    '--text-color': '#000000',
    '--list-value-text-color': 'rgba(0,0,0,0.4)',
    '--emergency-text-color': 'rgba(244, 63, 49, 1)',
    '--title-color': 'rgba(0,0,0,0.8)',
    '--home-background-color': '#f7f7f7',
    '--card-background-color': 'rgba(255,255,255,1)',
    '--button-text-color': '#000000',
    '--modal-background-color': '#ffffff',
    '--thinLine-background-color': 'rgba(0,0,0,0.1)',
    '--camera-background-color': 'rgba(247, 247, 247, 1)',
    '--subtitle-text-color': 'rgba(0, 0, 0, 0.6)',
    '--event-card-background-color': 'rgba(0,0,0,0.04)',
    '--modal-content-background-color': 'rgba(247, 247, 247, 1)',
    '--primary-color': 'rgba(52, 130, 255, 1)',
    '--primary-btn-background-color': 'rgba(52, 130, 255, 0.1)',
    '--cancel-btn-background-color': 'rgba(0, 0, 0, 0.06)',
    '--card-active-background-color': 'rgba(245, 245, 245, 1)',
    '--emergency-background-color': 'rgba(243, 0, 24, 0.1)',
    '--desktop-modal-bg-color': 'rgba(255, 255, 255, 1)',
    '--tab-active-bg-color': 'rgba(0,0,0,0.3)',
    '--side-bar-background-color': 'rgba(247, 247, 247, 1)',
    '--system-text-color': 'rgba(140, 147, 176, 1)',
    '--table-row-hover-color': 'rgba(0, 0, 0, 0.03)',
    '--primary-list-color': 'rgba(52, 130, 255, 0.1)',
    '--card-hover-color': 'rgba(239, 239, 239, 1)',
    '--tab-btn-color': 'rgba(85, 85, 85, 1)',
    '--list-value-text-color-reverse': 'rgba(255,255,255,0.4)',
    '--correction-disable-btn-color': 'rgba(0, 0, 0, 0.06)',
    '--correction-disable-title-color': 'rgba(0, 0, 0, 0.3)',
    '--all-library-bg': 'rgba(247, 247, 247, 1)',
    '--fat-card-hover-bg': 'rgba(0,0,0,0.03)',
    '--library-input-bg': 'rgba(247, 247, 247, 1)',
    '--library-title-color': 'rgba(0, 0, 0, 0.8)',
    '--library-input-text-color': 'rgba(0, 0, 0, 0.4)',
    '--library-button-bg': 'rgba( 52,130,255, 1)',
    '--library-switch-bg': 'rgba(230,230,230, 1)',
    '--file-selector-bg': 'rgba(255,255,255, 1)',
    '--user-selector-checkbox-bg': 'rgba(0, 0, 0, 0.06)',
    '--search-tab-active-bg': 'rgba( 235,243,255, 1)',
    '--search-tab-bg': 'rgba( 247,247,247, 1)',
    '--table-hover-bg': 'rgba(  247, 247, 247, 1)',
    '--add-lib-text-color': 'rgba( 0, 0, 0, 0.6)',
    '--componentcard-btn-bg-color': 'rgba(240,240,240,1)',
    '--componentcard-bg-color': 'rgba(255,255,255,1)',
    '--componentcard-title-bg-color': 'rgba(243, 248, 255, 1)',
    '--download-card-bg-color': 'rgba(0, 0, 0, 0.04)',
  },
  dark: {
    '--background-color': '#000000',
    '--text-color': '#ffffff',
    '--list-value-text-color': 'rgba(255,255,255,0.4)',
    '--emergency-text-color': 'rgba(244, 63, 49, 1)',
    '--title-color': 'rgba(255,255,255,0.8)',
    '--home-background-color': '#000000',
    '--button-text-color': '#ffffff',
    '--modal-background-color': 'rgba(44, 44, 46, 1)',
    '--thinLine-background-color': 'rgba(255,255,255,0.2)',
    '--card-background-color': 'rgba(255, 255, 255, 0.09)',
    '--camera-background-color': 'rgba(0,0,0,1)',
    '--subtitle-text-color': 'rgba(255, 255, 255, 0.6)',
    '--event-card-background-color': 'rgba(255,255,255,0.09)',
    '--modal-content-background-color': 'rgba(44, 44, 46, 1)',
    '--primary-color': 'rgba(52, 130, 255, 1)',
    '--primary-btn-background-color': 'rgba(52, 130, 255, 0.2)',
    '--cancel-btn-background-color': 'rgba(75, 75, 77, 1)',
    '--card-active-background-color': 'rgba(245, 245, 245, 0.04)',
    '--emergency-background-color': 'rgba(243, 0, 24, 0.2)',
    '--desktop-modal-bg-color': 'rgba(36, 36, 36, 1)',
    '--tab-active-bg-color': 'rgba(69,69,69,1)',
    '--side-bar-background-color': 'rgba(16, 16, 16, 1)',
    '--system-text-color': 'rgba(140, 147, 176, 1)',
    '--table-row-hover-color': 'rgba(255, 255, 255, 0.08)',
    '--primary-list-color': 'rgba(52, 130, 255, 0.2)',
    '--card-hover-color': 'rgba(239, 239, 239, 1)',
    '--tab-btn-color': 'rgba(85, 85, 85, 1)',
    '--list-value-text-color-reverse': 'rgba(0,0,0,0.4)',
    '--correction-disable-btn-color': 'rgba(39, 122, 247, 0.3)',
    '--correction-disable-title-color': 'rgba(255, 255, 255, 0.3)',
    '--all-library-bg': 'rgba(247, 247, 247, 0.1)',
    '--fat-card-hover-bg': 'rgba(255, 255, 255, 0.09)',
    '--library-input-bg': 'rgba(25, 25, 25, 1)',
    '--library-input-text-color': 'rgba(255, 255, 255, 0.4)',
    '--library-title-color': 'rgba(255, 255, 255, 0.8)',
    '--library-button-bg': 'rgba(   21, 52,102, 1)',
    '--library-switch-bg': 'rgba( 51, 51, 51, 0.8)',
    '--file-selector-bg': 'rgba(44, 44, 46, 1)',
    '--user-selector-checkbox-bg': 'rgba(255, 255, 255, 0.2)',
    '--search-tab-active-bg': 'rgba( 10, 26, 51, 1)',
    '--search-tab-bg': 'rgba(  25, 25, 25, 1)',
    '--table-hover-bg': 'rgba(  20, 20, 20, 1)',
    '--add-lib-text-color': 'rgba( 255, 255, 255, 0.6)',
    '--componentcard-btn-bg-color': 'rgba(44,44,46,1)',
    '--componentcard-bg-color': 'rgba(26,26,26,1)',
    '--componentcard-title-bg-color': 'rgba(36, 36, 36, 1)',
    '--download-card-bg-color': 'rgba(255, 255, 255, 0.12)',

  },
};

export default themes;